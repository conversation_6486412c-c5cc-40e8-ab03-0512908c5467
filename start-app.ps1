# Script PowerShell para iniciar Personal Trainer App
Write-Host "🚀 Iniciando Personal Trainer App..." -ForegroundColor Green
Write-Host ""

# Verificar se yarn está instalado
if (!(Get-Command yarn -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Yarn não encontrado. Instalando..." -ForegroundColor Red
    npm install -g yarn
}

# Iniciar React em background
Write-Host "📱 Iniciando React..." -ForegroundColor Yellow
$reactJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    yarn start
}

# Aguardar React carregar
Write-Host "⏳ Aguardando React carregar (25 segundos)..." -ForegroundColor Yellow
Start-Sleep -Seconds 25

# Verificar se React está rodando
$reactRunning = $false
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction SilentlyContinue
    if ($response.StatusCode -eq 200) {
        $reactRunning = $true
        Write-Host "✅ React está rodando!" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ React ainda não está pronto, mas continuando..." -ForegroundColor Yellow
}

# Iniciar Electron
Write-Host "🖥️ Iniciando Electron..." -ForegroundColor Cyan
try {
    npx electron .
} catch {
    Write-Host "❌ Erro ao iniciar Electron: $_" -ForegroundColor Red
}

# Limpar
Write-Host ""
Write-Host "🧹 Finalizando processos..." -ForegroundColor Yellow
Stop-Job $reactJob -ErrorAction SilentlyContinue
Remove-Job $reactJob -ErrorAction SilentlyContinue

Write-Host "✅ Aplicação finalizada." -ForegroundColor Green
Read-Host "Pressione Enter para sair"
