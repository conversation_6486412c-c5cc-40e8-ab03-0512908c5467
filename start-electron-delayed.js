// Script para iniciar Electron após delay
const { spawn } = require('child_process');

console.log('⏳ Aguardando React carregar (25 segundos)...');

setTimeout(() => {
  console.log('🖥️ Iniciando Electron...');
  
  const electron = spawn('npx', ['electron', '.'], {
    stdio: 'inherit',
    shell: true
  });
  
  electron.on('close', (code) => {
    console.log(`✅ Electron finalizado com código: ${code}`);
  });
  
  electron.on('error', (err) => {
    console.error('❌ Erro ao iniciar Electron:', err);
  });
  
}, 25000); // 25 segundos
